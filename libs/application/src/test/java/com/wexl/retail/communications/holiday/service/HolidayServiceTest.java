package com.wexl.retail.communications.holiday.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.communications.holiday.dto.HolidayDto;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import com.wexl.retail.erp.attendance.domain.SectionAttendance;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class HolidayServiceTest {

  @Mock private NotificationsService notificationService;
  @Mock private UserRepository userRepository;
  @Mock private NotificationRepository notificationRepository;
  @Mock private DateTimeUtil dateTimeUtil;
  @Mock private SectionAttendanceRepository sectionAttendanceRepository;
  @Mock private EventNotificationService eventNotificationService;
  @Mock private StudentRepository studentRepository;

  @InjectMocks private HolidayService holidayService;

  private HolidayDto.HolidayRequest holidayRequest;
  private String orgSlug;
  private String teacherAuthId;

  @BeforeEach
  void setUp() {
    orgSlug = "test-org";
    teacherAuthId = "teacher-123";
    
    // Create a holiday request for multiple grades
    holidayRequest = new HolidayDto.HolidayRequest(
        "Independence Day",
        "School will be closed for Independence Day celebration",
        Arrays.asList("grade-1", "grade-2", "grade-3"),
        LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
        LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
        Arrays.asList("attachment1.pdf"),
        Arrays.asList("http://example.com/holiday-info")
    );
  }

  @Test
  void testCreateHolidayNotification_ShouldCreateNotificationForEachGrade() {
    // Arrange
    List<SectionAttendance> attendanceList = Arrays.asList(
        createMockSectionAttendance(1L),
        createMockSectionAttendance(2L)
    );
    
    when(sectionAttendanceRepository.findAttendanceByDateRangeAndGrades(
        eq(orgSlug), anyInt(), anyInt(), eq(holidayRequest.gradeSlugs())))
        .thenReturn(attendanceList);

    // Act
    holidayService.createHolidayNotification(orgSlug, holidayRequest, teacherAuthId);

    // Assert
    // Verify that createNotificationByGrade is called once with all grades
    ArgumentCaptor<NotificationDto.NotificationRequest> notificationCaptor = 
        ArgumentCaptor.forClass(NotificationDto.NotificationRequest.class);
    verify(notificationService, times(1)).createNotificationByGrade(
        eq(orgSlug), notificationCaptor.capture(), eq(teacherAuthId));
    
    NotificationDto.NotificationRequest capturedRequest = notificationCaptor.getValue();
    assertEquals(holidayRequest.title(), capturedRequest.title());
    assertEquals(holidayRequest.message(), capturedRequest.message());
    assertEquals(holidayRequest.gradeSlugs(), capturedRequest.gradeSlugs());
    assertEquals(NotificationType.GRADE, capturedRequest.notificationType());
    assertEquals(CommunicationFeature.HOLIDAY, capturedRequest.feature());

    // Verify that sendPushNotificationWithGrade is called for each grade separately
    verify(eventNotificationService, times(3)).sendPushNotificationWithGrade(
        eq(orgSlug), 
        eq(holidayRequest.message()), 
        eq(holidayRequest.title()), 
        any(List.class));
    
    // Verify specific calls for each grade
    verify(eventNotificationService).sendPushNotificationWithGrade(
        orgSlug, holidayRequest.message(), holidayRequest.title(), Arrays.asList("grade-1"));
    verify(eventNotificationService).sendPushNotificationWithGrade(
        orgSlug, holidayRequest.message(), holidayRequest.title(), Arrays.asList("grade-2"));
    verify(eventNotificationService).sendPushNotificationWithGrade(
        orgSlug, holidayRequest.message(), holidayRequest.title(), Arrays.asList("grade-3"));

    // Verify attendance records are updated to HOLIDAY status (which maps to 'H')
    verify(sectionAttendanceRepository).saveAll(attendanceList);
    attendanceList.forEach(attendance -> 
        assertEquals(CompletionStatus.HOLIDAY, attendance.getStatus()));
  }

  @Test
  void testCreateHolidayNotification_ShouldHandleSingleGrade() {
    // Arrange
    HolidayDto.HolidayRequest singleGradeRequest = new HolidayDto.HolidayRequest(
        "Single Grade Holiday",
        "Holiday for grade 1 only",
        Arrays.asList("grade-1"),
        LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
        LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
        null,
        null
    );
    
    when(sectionAttendanceRepository.findAttendanceByDateRangeAndGrades(
        eq(orgSlug), anyInt(), anyInt(), eq(singleGradeRequest.gradeSlugs())))
        .thenReturn(Arrays.asList());

    // Act
    holidayService.createHolidayNotification(orgSlug, singleGradeRequest, teacherAuthId);

    // Assert
    verify(notificationService, times(1)).createNotificationByGrade(
        eq(orgSlug), any(NotificationDto.NotificationRequest.class), eq(teacherAuthId));
    
    // Verify only one push notification call for single grade
    verify(eventNotificationService, times(1)).sendPushNotificationWithGrade(
        orgSlug, singleGradeRequest.message(), singleGradeRequest.title(), Arrays.asList("grade-1"));
  }

  private SectionAttendance createMockSectionAttendance(Long id) {
    SectionAttendance attendance = new SectionAttendance();
    attendance.setId(id);
    attendance.setStatus(CompletionStatus.NOTCOMPLETED); // Initial status
    return attendance;
  }
}
