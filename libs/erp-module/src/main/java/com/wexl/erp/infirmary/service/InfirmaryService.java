package com.wexl.erp.infirmary.service;

import com.wexl.erp.infirmary.dto.InfirmaryEntryDto;
import com.wexl.erp.infirmary.model.InfirmaryEntry;
import com.wexl.erp.infirmary.repository.InfirmaryRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Data
public class InfirmaryService {
  private final InfirmaryRepository infirmaryRepository;
  private final StudentRepository studentRepository;
  private final DateTimeUtil dateTimeUtil;
  private static final String STUDENT_NOT_FOUND = "error.StudentNotFound";
  private static final String INFIRMARY_ENTRY_NOT_FOUND = "error.InfirmaryEntryNotFound";
  private final EventNotificationService eventNotificationService;
  private final UserRepository userRepository;
  private final TeacherRepository teacherRepository;
  private final NotificationsService notificationsService;
  private final AuthService authService;

  public void markEntry(InfirmaryEntryDto.Request request, String orgSlug) {
    Optional<Student> optionalStudent = studentRepository.findById(request.studentId());
    if (optionalStudent.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, STUDENT_NOT_FOUND);
    }
    Student student = optionalStudent.get();
    InfirmaryEntry entry =
        InfirmaryEntry.builder()
            .studentId(student.getId())
            .studentName(
                student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName())
            .grade(student.getSection().getGradeSlug())
            .date(dateTimeUtil.convertEpochToIso8601Legacy(request.date()).toLocalDate())
            .inTime(dateTimeUtil.convertEpochToIso8601Legacy(request.inTime()))
            .outTime(dateTimeUtil.convertEpochToIso8601Legacy(request.outTime()))
            .orgSlug(orgSlug)
            .remarks(request.remarks())
            .build();
    infirmaryRepository.save(entry);
    List<String> authIds = new ArrayList<>();
    List<Teacher> adminUsers = teacherRepository.getAllAdminsByOrg(orgSlug);
    List<Teacher> sectionTeachers =
        teacherRepository.getSectionTeachersBySectionUuids(List.of(student.getSection().getUuid()));

    authIds.addAll(
        sectionTeachers.stream().map(Teacher::getUserInfo).map(User::getAuthUserId).toList());
    adminUsers.forEach(user -> authIds.add(user.getUserInfo().getAuthUserId()));
    authIds.add(student.getUserInfo().getAuthUserId());

    log.info("sending notification to parent {}", student.getId());

    for (String authId : authIds) {
      notificationsService.createNotificationByStaff(
          orgSlug,
          NotificationDto.NotificationRequest.builder()
              .title("Infirmary Notification")
              .message(request.remarks())
              .notificationType(NotificationType.INDIVIDUAL)
                  .staffAuthId(authService.getTeacherDetails().getAuthUserId())
              .build(),
          authId,
          false);
      eventNotificationService.sendPushNotificationForUser(
          authId,
          request.remarks(),
          orgSlug,
          NotificationType.INDIVIDUAL,
          "Infirmary Notification");
    }
  }

  public List<InfirmaryEntryDto.Response> getAllInfirmaryEntriesOfStudent(Long studentId) {
    List<InfirmaryEntry> infirmaryEntries =
        infirmaryRepository.findAllByStudentIdOrderByIdDesc(studentId);
    List<InfirmaryEntryDto.Response> response = new ArrayList<>();
    infirmaryEntries.forEach(
        entry -> {
          response.add(
              InfirmaryEntryDto.Response.builder()
                  .studentId(entry.getStudentId())
                  .infirmaryId(entry.getId())
                  .studentName(entry.getStudentName())
                  .grade(entry.getGrade())
                  .date(DateTimeUtil.convertIso8601ToEpoch(entry.getDate().atStartOfDay()))
                  .inTime(DateTimeUtil.convertIso8601ToEpoch(entry.getInTime()))
                  .outTime(DateTimeUtil.convertIso8601ToEpoch(entry.getOutTime()))
                  .remarks(entry.getRemarks())
                  .build());
        });
    return response;
  }

  public void updateInfirmaryEntry(InfirmaryEntryDto.Request request) {
    Optional<InfirmaryEntry> optionalInfirmaryEntry =
        infirmaryRepository.findById(request.infirmaryId());
    if (optionalInfirmaryEntry.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, INFIRMARY_ENTRY_NOT_FOUND);
    }
    InfirmaryEntry infirmaryEntry = optionalInfirmaryEntry.get();
    infirmaryEntry.setDate(dateTimeUtil.convertEpochToIso8601Legacy(request.date()).toLocalDate());
    infirmaryEntry.setInTime(dateTimeUtil.convertEpochToIso8601Legacy(request.inTime()));
    infirmaryEntry.setOutTime(dateTimeUtil.convertEpochToIso8601Legacy(request.outTime()));
    infirmaryEntry.setRemarks(request.remarks());
    infirmaryRepository.save(infirmaryEntry);
  }

  public void deleteInfirmaryEntry(Long infirmaryId) {
    infirmaryRepository.deleteById(infirmaryId);
  }

  public List<InfirmaryEntryDto.Response> getAllInfirmaryEntriesOfOrg(String orgSlug) {
    List<InfirmaryEntry> infirmaryEntries = infirmaryRepository.findAllByOrgSlug(orgSlug);
    List<InfirmaryEntryDto.Response> response = new ArrayList<>();
    infirmaryEntries.forEach(
        entry -> {
          response.add(
              InfirmaryEntryDto.Response.builder()
                  .studentId(entry.getStudentId())
                  .infirmaryId(entry.getId())
                  .studentName(entry.getStudentName())
                  .grade(entry.getGrade())
                  .date(DateTimeUtil.convertIso8601ToEpoch(entry.getDate().atStartOfDay()))
                  .inTime(DateTimeUtil.convertIso8601ToEpoch(entry.getInTime()))
                  .outTime(DateTimeUtil.convertIso8601ToEpoch(entry.getOutTime()))
                  .remarks(entry.getRemarks())
                  .build());
        });
    return response;
  }

  public List<InfirmaryEntryDto.StudentInfo> getStudentInfo(String orgSlug, String userName) {
    var students = studentRepository.getStudentInfo(userName + "%", orgSlug);
    return students.stream()
        .map(
            student ->
                InfirmaryEntryDto.StudentInfo.builder()
                    .studentId(student.getStudentId())
                    .studentName(student.getStudentName())
                    .grade(student.getGrade())
                    .userName(student.getUserName())
                    .section(student.getSectionName())
                    .build())
        .toList();
  }

  public List<InfirmaryEntryDto.Response> getAllStudentsBySectionAndGrade(
      String orgSlug, String authUserId, String gradeSlug, String sectionUuid) {
    var user = userRepository.findByAuthUserId(authUserId);

    if (user.isEmpty()) {
      throw new RuntimeException("User not found ");
    }

    if (UserRoleHelper.get().isOrgAdmin(user.get()) || UserRoleHelper.get().isTeacher(user.get())) {
      return infirmaryRepository
          .findAllByTeacherGradeAndSection(orgSlug, gradeSlug, sectionUuid)
          .stream()
          .map(
              entry ->
                  InfirmaryEntryDto.Response.builder()
                      .studentId(entry.getStudentId())
                      .infirmaryId(entry.getId())
                      .studentName(entry.getStudentName())
                      .grade(entry.getGrade())
                      .date(DateTimeUtil.convertIso8601ToEpoch(entry.getDate().atStartOfDay()))
                      .inTime(DateTimeUtil.convertIso8601ToEpoch(entry.getInTime()))
                      .outTime(DateTimeUtil.convertIso8601ToEpoch(entry.getOutTime()))
                      .remarks(entry.getRemarks())
                      .build())
          .toList();
    } else {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.AccessDenied");
    }
  }

  public List<InfirmaryEntryDto.Response> getInfirmaryEntriesByStudentName(
      String searchKey, String authUserId, String orgSlug) {

    User authUser = userRepository.getUserByAuthUserId(authUserId);

    boolean isAdmin = UserRoleHelper.get().isOrgAdmin(authUser);
    List<Long> teacherSectionIds =
        isAdmin
            ? null
            : authUser.getTeacherInfo().getSections().stream().map(Section::getId).toList();

    List<User> matchingUsers =
        userRepository.findByOrgSlugAndSearchKey(orgSlug, "%" + searchKey + "%");

    List<Student> matchedStudents =
        matchingUsers.stream()
            .filter(user -> user.getStudentInfo() != null)
            .map(User::getStudentInfo)
            .toList();

    List<InfirmaryEntry> infirmaryEntries =
        matchedStudents.stream()
            .flatMap(
                student ->
                    infirmaryRepository
                        .findByStudentIdAndOrgSlug(student.getId(), orgSlug)
                        .stream())
            .collect(Collectors.toList());
    if (!isAdmin && teacherSectionIds != null) {
      infirmaryEntries =
          infirmaryEntries.stream()
              .filter(
                  entry -> {
                    Student student =
                        matchedStudents.stream()
                            .filter(s -> s.getId() == (entry.getStudentId()))
                            .findFirst()
                            .orElse(null);
                    return student != null
                        && teacherSectionIds.contains(student.getSection().getId());
                  })
              .collect(Collectors.toList());
    }
    return infirmaryEntries.stream()
        .map(
            entry ->
                InfirmaryEntryDto.Response.builder()
                    .infirmaryId(entry.getId())
                    .studentId(entry.getStudentId())
                    .studentName(entry.getStudentName())
                    .grade(entry.getGrade())
                    .date(DateTimeUtil.convertIso8601ToEpoch(entry.getDate().atStartOfDay()))
                    .inTime(DateTimeUtil.convertIso8601ToEpoch(entry.getInTime()))
                    .outTime(DateTimeUtil.convertIso8601ToEpoch(entry.getOutTime()))
                    .remarks(entry.getRemarks())
                    .build())
        .toList();
  }
}
